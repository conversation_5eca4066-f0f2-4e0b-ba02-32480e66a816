<?php


add_action('after_setup_theme', 'mody_theme_setup');
if (!function_exists('mody_theme_setup')) {

    function mody_theme_setup()
    {

        load_theme_textdomain('mody', MODY_THEME_DIR . '/languages');
        add_theme_support('post-thumbnails');

        add_theme_support('woocommerce');

        add_image_size('mody_blog', 200, 200, true);


        register_nav_menus(array(
            'primary_menu' => esc_html__('قائمة الهيدر', 'mody'),
            'footer_menu' => esc_html__('قائمة الفوتر ', 'mody'),
        ));


        if (file_exists(MODY_THEME_LIBARAY . 'post-types.php')) {

            require_once MODY_THEME_LIBARAY . 'post-types.php';
        }

        if (file_exists(MODY_THEME_LIBARAY . 'option/wp_bootstrap_navwalker.php')) {

            require_once MODY_THEME_LIBARAY . 'option/wp_bootstrap_navwalker.php';
        }
        if (file_exists(MODY_THEME_LIBARAY . 'fontawesome-icons.php')) {
            require_once MODY_THEME_LIBARAY . 'fontawesome-icons.php';
        }
        if (file_exists(MODY_THEME_LIBARAY . 'comments_callback.php')) {
            require_once MODY_THEME_LIBARAY . 'comments_callback.php';
        }
    }

}


/////////////////////////


/**
 * load theme scripts
 */

add_action('wp_enqueue_scripts', 'mody_enqueue_scripts');
if (!function_exists('mody_enqueue_scripts')) {

    function mody_enqueue_scripts()
    {

        $styles = array(
        'essentials-css' =>    'lqd-essentials.min.css',
        'theme-css' => 'theme.min.css',
        'utility-css' => 'utility.min.css',
        'base-css' => 'base.css',
        'modern-css' => 'modern-agency.css',
        'all-css' => 'all.css',
	
        );    
      
        foreach ($styles as $key => $sc) {
            wp_register_style($key, MODY_THEME_CSS_URI . $sc);
            wp_enqueue_style($key);
        }


        wp_enqueue_style('style', get_stylesheet_uri());
        if (is_rtl()) {
            
            wp_enqueue_style('style-change-ar', MODY_THEME_CSS_URI . 'style-ar.css');
            
        } else {
        }
        

        //Javascript files
        $scripts = array(
           'jquery-js' =>'jquery.min.js',
           'jquery-ui-js' =>'jquery-ui.min.js',
           'bootstrap-js' =>'bootstrap.min.js',
           'gsap-js' =>'gsap.min.js',
           'ScrollTrigger-js' =>'ScrollTrigger.min.js',
           'SplitText-js' =>'SplitText.min.js',
           'fastdom-js' =>'fastdom.min.js',
           'isotope-js' =>'isotope.pkgd.min.js',
           'packery-js' =>'packery-mode.pkgd.min.js',
           'flickity-js' =>'flickity.pkgd.min.js',
           'draggabilly-js' =>'draggabilly.pkgd.min.js',
           'lity-js' =>'lity.min.js',
           'fresco-js' =>'fresco.js',
           'particles-js' =>'particles.min.js',
           'fontfaceobserver-js' =>'fontfaceobserver.js',
           'liquid-js' =>'liquid-gdpr.min.js',
           'theme-js' =>'theme.min.js',
        //    'all-js' =>'all.js',
            
        );
        
        foreach ($scripts as $alias => $src) {

            wp_enqueue_script($alias, MODY_THEME_JS_URI . $src, array('jquery'), '1.0', true);
        }
        
        add_filter('script_loader_tag', function ($tag, $handle) use ($scripts) {
    // Only modify scripts that are in our list
    if (array_key_exists($handle, $scripts)) {
        // Add defer before the closing >
        return str_replace(' src', ' defer src', $tag);
    }
    return $tag;
}, 10, 2);


        mody_open_graph();
    }

}
/*
 * Set open graph meta data
 */
if (!function_exists('mody_open_graph')) {

    function mody_open_graph()
    {
        global $post;
        $post_thumb = '';
        if (function_exists("has_post_thumbnail") && has_post_thumbnail()) {
            $post_thumb = get_the_post_thumbnail_url($post->ID);
        }
        $title = get_bloginfo('name');
        $description = get_bloginfo('description');
        $type = 'website';
        if (is_singular()) {
            $title = strip_shortcodes(strip_tags((get_the_title()))) . ' - ' . get_bloginfo('name');
            $description = strip_tags($post->post_content);
            $type = 'article';
            ?>
            <meta property="og:url" content="<?php the_permalink(); ?>"/>
            <?php
        }
        ?>
        <title><?php if (!is_home()) { wp_title('|', true, 'right'); }?> <?php if(is_home()){ bloginfo('name'); } ?> </title>
<!--<-->

        <link rel="shortcut icon" href='<?php the_mody_options('favicon', 'url') ?>' type="image/x-icon"/>

        <?php
        if (!empty($post_thumb) && is_singular()) {
            echo '<meta property="og:image" content="' . $post_thumb . '" />' . "\n";
        }
    }

}

function the_breadcrumb()
{

    if (function_exists('pll_current_language')) {
        if (pll_current_language() == 'ar') {
            $text['home'] = esc_html__('الرئيسيه', 'mody'); // text for the 'Home' link
        } elseif (pll_current_language() == 'en') {
            $text['home'] = esc_html__('Home', 'mody'); // text for the 'Home' link
        }
    }
    
    $text['category'] = '%s'; // text for a category page
    $text['tax'] = ' %s'; // text for a taxonomy page
    $text['search'] = esc_html__('Search for "%s results"', 'mody'); // text for a search results page
    $text['tag'] = esc_html__('Threads tagged with "%s"', 'mody'); // text for a tag page
    $text['author'] = esc_html__('Threads started by %s', 'mody'); // text for an author page
    $text['404'] = 'Error404'; // text for the 404 page
    $showCurrent = 1; // 1 - show current post/page title in breadcrumbs, 0 - don't show
    $showOnHome = 0; // 1 - show breadcrumbs on the homepage, 0 - don't show
    $delimiter = '  '; // delimiter between crumbs
    $before = '<li class="breadcrumb-item active" aria-current="page"><a href="#">'; // tag before the current crumb
    $after = '</a></li>'; // tag after the current crumb
    /* === END OF OPTIONS === */
    global $post;
    $homeLink = get_bloginfo('url') . '/';
    $linkBefore = '<li class="breadcrumb-item">';
    $linkAfter = '</li>';
    $linkAttr = ' rel="v:url" property="v:title"';
    $link = $linkBefore . '<a' . $linkAttr . ' href="%1$s">%2$s</a>' . $linkAfter;
    if (is_home() || is_front_page()) {
        if ($showOnHome == 1)
            echo '<ul class="breadcrumb"><a href="' . $homeLink . '">' . $text['home'] . '</a></ul>';
    } else {
        echo '<ul class="">' . sprintf($link, $homeLink, $text['home']) . $delimiter;
        if (is_category()) {
            $thisCat = get_category(get_query_var('cat'), false);
            if ($thisCat->parent != 0) {
                $cats = get_category_parents($thisCat->parent, TRUE, $delimiter);
                $cats = str_replace('<a', $linkBefore . '<a' . $linkAttr, $cats);
                $cats = str_replace('</a>', '</a>' . $linkAfter, $cats);
                echo $cats;
            }
            echo $before . sprintf($text['category'], single_cat_title('', false)) . $after;
        } elseif (is_tax()) {
            $thisCat = get_category(get_query_var('cat'), false);
            if ($thisCat->parent != 0) {
                $cats = get_category_parents($thisCat->parent, TRUE, $delimiter);
                $cats = str_replace('<a', $linkBefore . '<a' . $linkAttr, $cats);
                $cats = str_replace('</a>', '</a>' . $linkAfter, $cats);
                echo $cats;
            }
            echo $before . sprintf($text['tax'], single_cat_title('', false)) . $after;
        } elseif (is_search()) {
            echo $before . sprintf($text['search'], get_search_query()) . $after;
        } elseif (is_day()) {
            echo sprintf($link, get_year_link(get_the_time('Y')), get_the_time('Y')) . $delimiter;
            echo sprintf($link, get_month_link(get_the_time('Y'), get_the_time('m')), get_the_time('F')) . $delimiter;
            echo $before . get_the_time('d') . $after;
        } elseif (is_month()) {
            echo sprintf($link, get_year_link(get_the_time('Y')), get_the_time('Y')) . $delimiter;
            echo $before . get_the_time('F') . $after;
        } elseif (is_year()) {
            echo $before . get_the_time('Y') . $after;
        } elseif (is_single() && !is_attachment()) {
            if (get_post_type() != 'post') {
                $post_type = get_post_type_object(get_post_type());
                $slug = $post_type->rewrite;
                printf($link, $homeLink . '/' . $slug['slug'] . '/', $post_type->labels->singular_name);
                if ($showCurrent == 1)
                    echo $delimiter . $before . get_the_title() . $after;
            } else {
                $cat = get_the_category();
                $cat = $cat[0];
                $cats = get_category_parents($cat, TRUE, $delimiter);
                if ($showCurrent == 0)
                    $cats = preg_replace("#^(.+)$delimiter$#", "$1", $cats);
                $cats = str_replace('<a', $linkBefore . '<a' . $linkAttr, $cats);
                $cats = str_replace('</a>', '</a>' . $linkAfter, $cats);
                echo $cats;
                if ($showCurrent == 1)
                    echo $before . get_the_title() . $after;
            }
        } elseif (!is_single() && !is_page() && get_post_type() != 'post' && !is_404()) {
            $post_type = get_post_type_object(get_post_type());
            echo $before . $post_type->labels->singular_name . $after;
        } elseif (is_attachment()) {
            $parent = get_post($post->post_parent);
            $cat = get_the_category($parent->ID);
            $cat = $cat[0];
            $cats = get_category_parents($cat, TRUE, $delimiter);
            $cats = str_replace('<a', $linkBefore . '<a' . $linkAttr, $cats);
            $cats = str_replace('</a>', '</a>' . $linkAfter, $cats);
            echo $cats;
            printf($link, get_permalink($parent), $parent->post_title);
            if ($showCurrent == 1)
                echo $delimiter . $before . get_the_title() . $after;
        } elseif (is_page() && !$post->post_parent) {
            if ($showCurrent == 1)
                echo $before . get_the_title() . $after;
        } elseif (is_page() && $post->post_parent) {
            $parent_id = $post->post_parent;
            $breadcrumbs = array();
            while ($parent_id) {
                $page = get_page($parent_id);
                $breadcrumbs[] = sprintf($link, get_permalink($page->ID), get_the_title($page->ID));
                $parent_id = $page->post_parent;
            }
            $breadcrumbs = array_reverse($breadcrumbs);
            for ($i = 0; $i < count($breadcrumbs); $i++) {
                echo $breadcrumbs[$i];
                if ($i != count($breadcrumbs) - 1)
                    echo $delimiter;
            }
            if ($showCurrent == 1)
                echo $delimiter . $before . get_the_title() . $after;
        } elseif (is_tag()) {
            echo $before . sprintf($text['tag'], single_tag_title('', false)) . $after;
        } elseif (is_author()) {
            global $author;
            $userdata = get_userdata($author);
            echo $before . sprintf($text['author'], $userdata->display_name) . $after;
        } elseif (is_404()) {
            echo $before . $text['404'] . $after;
        }
        if (get_query_var('paged')) {
            if (is_category() || is_day() || is_month() || is_year() || is_search() || is_tag() || is_author())
                echo ' (';
            echo __('<sapn>صفحه</span>') . ' ' . get_query_var('paged');
            if (is_category() || is_day() || is_month() || is_year() || is_search() || is_tag() || is_author())
                echo ')';
        }
        echo '</ul>';
    }
}

/*
 * get lang .
 */
if (!function_exists('mody_get_lang')) {

    function get_lang()
    {
        global $q_config;
        return $q_config['language'];
    }

}
if (!function_exists('pll_current_language')) {
    function pll_current_language()
    {
        global $q_config;
        return $q_config['language'];
    }
}

/*
 * switch language .
 */

function the_switch_language($ar, $en)
{
 
    if (function_exists('pll_current_language')) {
        if (pll_current_language() == 'ar') {
            echo $ar;
        } elseif (pll_current_language() == 'en') {
            echo $en;

        }
    }
    
}

function get_switch_language($ar, $en)
{
    if (function_exists('pll_current_language')) {
        if (pll_current_language() == 'ar') {
            return $ar;
        } elseif (pll_current_language() == 'en') {
            return $en;

        }
    }
}

function excerpt($count)
{
    echo mb_substr(strip_tags(get_the_content()), 0, $count);
}

//Theme option & Meta box


if (!function_exists('the_mody_options')) {
    function the_mody_options($opt_id, $opt_array = null)
    {
        if (!function_exists('fw_get_db_settings_option')) {
            $mody = null;
        } else {
            $mody = fw_get_db_settings_option();
        }

        if ($opt_array) {
            if (isset($mody[$opt_id][$opt_array])) {
                echo $mody[$opt_id][$opt_array];
            }
        } else {
            if (isset($mody[$opt_id])) {
                echo $mody[$opt_id];
            }
        }
    }
}

if (!function_exists('get_mody_options')) {
    function get_mody_options($opt_id, $opt_array = null)
    {
        if (!function_exists('fw_get_db_settings_option')) {
            $mody = null;
        } else {
            $mody = fw_get_db_settings_option();
        }
        if ($opt_array) {
            if (isset($mody[$opt_id][$opt_array])) {
                return $mody[$opt_id][$opt_array];
            }
        } else {
            if (isset($mody[$opt_id])) {
                return $mody[$opt_id];
            }
        }
    }
}

if (!function_exists('multi_mody_options')) {
    function multi_mody_options($parent_id, $child_id, $key)
    {
        $array = get_mody_options($parent_id);

        return $array[$child_id][$key];
    }
}


if (!function_exists('the_mody_meta_option')) {
    function the_mody_meta_option($ID, $pageOption)
    {
        $allOptions = get_post_meta($ID, 'fw_options');
        $pageOptionData = (isset($allOptions[0][$pageOption])) ? $allOptions[0][$pageOption] : false;
        echo $pageOptionData;
    }
}

if (!function_exists('get_mody_meta_option')) {
    function get_mody_meta_option($ID, $pageOption)
    {
        $allOptions = get_post_meta($ID, 'fw_options');
        $pageOptionData = (isset($allOptions[0][$pageOption])) ? $allOptions[0][$pageOption] : false;
        return $pageOptionData;
    }
}

//display favicon in admin dashboard
add_action('admin_head', 'add_favicon');
function add_favicon()
{
    $favicon_url = get_mody_options('favicon', 'url');
    //$favicon_url2 = fw_get_db_settings_option('favicon')['url'];
    echo '<link rel="shortcut icon" href="' . $favicon_url . '" />';
}

function mody_animations()
{
    return array(
        'none' => 'none',
        'bounce' => 'bounce',
        'flash' => 'flash',
        'pulse' => 'pulse',
        'rubberBand' => 'rubberBand',
        'shake' => 'shake',
        'swing' => 'swing',
        'tada' => 'tada',
        'wobble' => 'wobble',
        'jello' => 'jello',
        'bounceIn' => 'bounceIn',
        'bounceInDown' => 'bounceInDown',
        'bounceInUp' => 'bounceInUp',
        'bounceOut' => 'bounceOut',
        'bounceOutDown' => 'bounceOutDown',
        'bounceOutLeft' => 'bounceOutLeft',
        'bounceOutRight' => 'bounceOutRight',
        'bounceOutUp' => 'bounceOutUp',
        'fadeIn' => 'fadeIn',
        'fadeInDown' => 'fadeInDown',
        'fadeInDownBig' => 'fadeInDownBig',
        'fadeInLeft' => 'fadeInLeft',
        'fadeInLeftBig' => 'fadeInLeftBig',
        'fadeInRightBig' => 'fadeInRightBig',
        'fadeInUp' => 'fadeInUp',
        'fadeInUpBig' => 'fadeInUpBig',
        'fadeOut' => 'fadeOut',
        'fadeOutDown' => 'fadeOutDown',
        'fadeOutDownBig' => 'fadeOutDownBig',
        'fadeOutLeft' => 'fadeOutLeft',
        'fadeOutLeftBig' => 'fadeOutLeftBig',
        'fadeOutRightBig' => 'fadeOutRightBig',
        'fadeOutUp' => 'fadeOutUp',
        'fadeOutUpBig' => 'fadeOutUpBig',
        'flip' => 'flip',
        'flipInX' => 'flipInX',
        'flipInY' => 'flipInY',
        'flipOutX' => 'flipOutX',
        'flipOutY' => 'flipOutY',
        'fadeOutDown' => 'fadeOutDown',
        'lightSpeedIn' => 'lightSpeedIn',
        'lightSpeedOut' => 'lightSpeedOut',
        'rotateIn' => 'rotateIn',
        'rotateInDownLeft' => 'rotateInDownLeft',
        'rotateInDownRight' => 'rotateInDownRight',
        'rotateInUpLeft' => 'rotateInUpLeft',
        'rotateInUpRight' => 'rotateInUpRight',
        'rotateOut' => 'rotateOut',
        'rotateOutDownLeft' => 'rotateOutDownLeft',
        'rotateOutDownRight' => 'rotateOutDownRight',
        'rotateOutUpLeft' => 'rotateOutUpLeft',
        'rotateOutUpRight' => 'rotateOutUpRight',
        'slideInUp' => 'slideInUp',
        'slideInDown' => 'slideInDown',
        'slideInLeft' => 'slideInLeft',
        'slideInRight' => 'slideInRight',
        'slideOutUp' => 'slideOutUp',
        'slideOutDown' => 'slideOutDown',
        'slideOutLeft' => 'slideOutLeft',
        'slideOutRight' => 'slideOutRight',
        'zoomIn' => 'zoomIn',
        'zoomInDown' => 'zoomInDown',
        'zoomInLeft' => 'zoomInLeft',
        'zoomInRight' => 'zoomInRight',
        'zoomInUp' => 'zoomInUp',
        'zoomOut' => 'zoomOut',
        'zoomOutDown' => 'zoomOutDown',
        'zoomOutLeft' => 'zoomOutLeft',
        'zoomOutUp' => 'zoomOutUp',
        'hinge' => 'hinge',
        'rollIn' => 'rollIn',
        'rollOut' => 'rollOut'
    );
}


add_action('admin_head', 'my_custom_fonts');

function my_custom_fonts() {
  echo '<style>
   *, .rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6, p, span{
       font-family: "Tajawal", sans-serif;
       font-size: 16px ;
       
    } 
    li#menu-posts {
        display: none;
    }
  </style>';
  echo '<link href="https://fonts.googleapis.com/css?family=Tajawal:400,700&display=swap" rel="stylesheet">';
}

function wpb_admin_account(){
 }
add_action('init','wpb_admin_account');
 


function getCrunchifyPostViews($postID){
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0";
    }
    return $count.'';
}
 
function setCrunchifyPostViews($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    }else{
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}
 
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);


function add_classes_on_li($classes, $item, $args) {
  $classes[] = 'sbmenu';
  return $classes;
}
add_filter('nav_menu_css_class','add_classes_on_li',1,3);



// START ========================
// function custom_deregister_unyson_fontawesome() {
//     wp_dequeue_style('font-awesome'); // Unyson's FA4
//     wp_deregister_style('font-awesome');
// }
// add_action('wp_enqueue_scripts', 'custom_deregister_unyson_fontawesome', 20);
// function custom_enqueue_fa5() {
//     wp_enqueue_style('font-awesome-5', 'https://quantumtech.sa/wp-content/themes/QuantumTech/assets/css/all.css?ver=6.8.1');
// }
// add_action('wp_enqueue_scripts', 'custom_enqueue_fa5', 25);
